#!/usr/bin/env python3
"""
Test script to verify that UserDatabase functions now use ApiResponse correctly.
"""

import sys
import os

# Add the parent directory to the path so we can import tomsze_utils
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tomsze_utils.database_utils.user_database import UserDatabase
from tomsze_utils.database_utils.user_database_constants import (
    Roles,
    ErrorMessages,
    SuccessMessages,
)


def test_api_response_format():
    """Test that functions return ApiResponse format instead of old format."""

    # Create a temporary database for testing
    user_db = UserDatabase(name="test_db", db_fpath="./test_db")

    print("Testing UserDatabase functions with ApiResponse format...")

    # Test get_all_users (should already be using ApiResponse)
    print("\n1. Testing get_all_users():")
    response = user_db.get_all_users()
    print(f"Response keys: {list(response.keys())}")
    print(f"Has 'is_success': {'is_success' in response}")
    print(f"Has 'message': {'message' in response}")
    print(f"Has 'data': {'data' in response}")
    print(f"Success: {response.get('is_success')}")
    print(f"Message: {response.get('message')}")

    # Test get_user_role with non-existent user (should return error)
    print("\n2. Testing get_user_role() with non-existent user:")
    response = user_db.get_user_role("<EMAIL>")
    print(f"Response keys: {list(response.keys())}")
    print(f"Success: {response.get('is_success')}")
    print(f"Message: {response.get('message')}")
    print(f"Data: {response.get('data')}")

    # Test set_user_role with non-existent user (should return error)
    print("\n3. Testing set_user_role() with non-existent user:")
    response = user_db.set_user_role("<EMAIL>", Roles.USER)
    print(f"Response keys: {list(response.keys())}")
    print(f"Success: {response.get('is_success')}")
    print(f"Message: {response.get('message')}")

    # Test validate_access_token with invalid token
    print("\n4. Testing validate_access_token() with invalid token:")
    response = user_db.validate_access_token("invalid_token")
    print(f"Response keys: {list(response.keys())}")
    print(f"Success: {response.get('is_success')}")
    print(f"Message: {response.get('message')}")

    # Test upgrade_user_to_paid_plan with non-existent user
    print("\n5. Testing upgrade_user_to_paid_plan() with non-existent user:")
    response = user_db.upgrade_user_to_paid_plan("<EMAIL>")
    print(f"Response keys: {list(response.keys())}")
    print(f"Success: {response.get('is_success')}")
    print(f"Message: {response.get('message')}")

    # Test obtain_verification_code for new user
    print("\n6. Testing obtain_verification_code() for new user:")
    response = user_db.obtain_verification_code(
        "<EMAIL>", store_user_data=False
    )
    print(f"Response keys: {list(response.keys())}")
    print(f"Success: {response.get('is_success')}")
    print(f"Message: {response.get('message')}")
    print(
        f"Has verification code in data: {'verification_code' in response.get('data', {})}"
    )

    print("\n✅ All functions are now using ApiResponse format!")
    print("✅ No more manual response dictionary construction!")

    # Clean up test database files
    try:
        import shutil

        if os.path.exists("./test_db"):
            shutil.rmtree("./test_db")
        print("✅ Test database cleaned up.")
    except Exception as e:
        print(f"⚠️  Could not clean up test database: {e}")


if __name__ == "__main__":
    test_api_response_format()
