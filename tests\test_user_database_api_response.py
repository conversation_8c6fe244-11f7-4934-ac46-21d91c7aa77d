#!/usr/bin/env python3
"""
Test script to verify that UserDatabase functions now use ApiResponse correctly.
"""

import sys
import os

# Add the parent directory to the path so we can import tomsze_utils
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tomsze_utils.database_utils.user_database import UserDatabase
from tomsze_utils.database_utils.user_database_constants import (
    Roles,
    ErrorMessages,
    SuccessMessages,
)


def assert_api_response_format(response, expected_success=None, expected_message=None):
    """Assert that response follows ApiResponse format."""
    # Check that response has the correct structure
    assert isinstance(
        response, dict
    ), f"Response should be a dict, got {type(response)}"

    # Check required keys
    assert "is_success" in response, "Response must have 'is_success' key"
    assert "message" in response, "Response must have 'message' key"

    # Check that old format keys are NOT present
    assert "db_result" not in response, "Response should not have old 'db_result' key"

    # Check expected values if provided
    if expected_success is not None:
        assert (
            response["is_success"] == expected_success
        ), f"Expected is_success={expected_success}, got {response['is_success']}"

    if expected_message is not None:
        assert (
            response["message"] == expected_message
        ), f"Expected message='{expected_message}', got '{response['message']}'"

    print(
        f"✅ ApiResponse format verified: success={response['is_success']}, message='{response['message']}'"
    )


def test_api_response_format():
    """Test that functions return ApiResponse format instead of old format."""

    # Create a temporary database for testing
    user_db = UserDatabase(name="test_db", db_fpath="./test_db")

    print("Testing UserDatabase functions with ApiResponse format...")

    # Test 1: get_all_users (should return success)
    print("\n1. Testing get_all_users():")
    response = user_db.get_all_users()
    assert_api_response_format(
        response,
        expected_success=True,
        expected_message=SuccessMessages.USERS_RETRIEVED,
    )
    assert "data" in response, "get_all_users should have 'data' key"
    assert "users" in response["data"], "get_all_users data should contain 'users' key"

    # Test 2: get_user_role with non-existent user (should return error)
    print("\n2. Testing get_user_role() with non-existent user:")
    response = user_db.get_user_role("<EMAIL>")
    assert_api_response_format(
        response, expected_success=False, expected_message=ErrorMessages.USER_NOT_FOUND
    )

    # Test 3: set_user_role with non-existent user (should return error)
    print("\n3. Testing set_user_role() with non-existent user:")
    response = user_db.set_user_role("<EMAIL>", Roles.USER)
    assert_api_response_format(
        response, expected_success=False, expected_message=ErrorMessages.USER_NOT_FOUND
    )

    # Test 4: validate_access_token with invalid token (should return error)
    print("\n4. Testing validate_access_token() with invalid token:")
    response = user_db.validate_access_token("invalid_token")
    assert_api_response_format(response, expected_success=False)
    # Message could be various token error messages, so we don't check exact message

    # Test 5: upgrade_user_to_paid_plan with non-existent user (should return error)
    print("\n5. Testing upgrade_user_to_paid_plan() with non-existent user:")
    response = user_db.upgrade_user_to_paid_plan("<EMAIL>")
    assert_api_response_format(
        response, expected_success=False, expected_message=ErrorMessages.USER_NOT_FOUND
    )

    # Test 6: obtain_verification_code for new user (should return success)
    print("\n6. Testing obtain_verification_code() for new user:")
    response = user_db.obtain_verification_code(
        "<EMAIL>", store_user_data=False
    )
    assert_api_response_format(
        response,
        expected_success=True,
        expected_message=SuccessMessages.GENERATED_VERIFICATION_CODE,
    )
    assert "data" in response, "obtain_verification_code should have 'data' key"
    assert (
        "verification_code" in response["data"]
    ), "obtain_verification_code data should contain 'verification_code' key"

    # Test 7: set_user_use_count with non-existent user (should return error)
    print("\n7. Testing set_user_use_count() with non-existent user:")
    response = user_db.set_user_use_count("<EMAIL>", 10)
    assert_api_response_format(
        response, expected_success=False, expected_message=ErrorMessages.USER_NOT_FOUND
    )

    # Test 8: get_user_use_count with non-existent user (should return error)
    print("\n8. Testing get_user_use_count() with non-existent user:")
    response = user_db.get_user_use_count("<EMAIL>")
    assert_api_response_format(
        response, expected_success=False, expected_message=ErrorMessages.USER_NOT_FOUND
    )

    print("\n✅ All functions are now using ApiResponse format!")
    print("✅ No more manual response dictionary construction!")
    print("✅ All assertions passed!")

    # Clean up test database files
    try:
        import shutil

        if os.path.exists("./test_db"):
            shutil.rmtree("./test_db")
        print("✅ Test database cleaned up.")
    except Exception as e:
        print(f"⚠️  Could not clean up test database: {e}")


if __name__ == "__main__":
    test_api_response_format()
