import hashlib
import datetime
import os
from typing import Any, Dict, List
from tomsze_utils import auth_utils
from tomsze_utils.auth_utils import (
    create_verification_code,
    hash_password,
    send_verification_email_with_code_only,
    verify_password,
)
from tomsze_utils.database_utils.pickle_database_split import PickleDatabaseSplit
from tomsze_utils.database_utils.user_database_constants import (
    Constants,
    ErrorMessages,
    LoggedInStatus,
    Plans,
    ResponseKeys,
    Roles,
    SuccessMessages,
    UserDataKeys,
)
from tomsze_utils.randome_utils import generate_random_integer_string
from tomsze_utils.api_response_utils import ApiResponse


class UserDatabase:
    def __init__(
        self,
        name=Constants.USER_DB_NAME,
        db_fpath: str = None,
    ):
        self.db = PickleDatabaseSplit(
            db_name=name,
            db_fpath=db_fpath,
            load_by_thread=False,
            use_sync_by_lock_file=True,
        )
        self.part_data_count = 100  # Adjust based on expected user count
        self.db.part_data_count = self.part_data_count
        self.password_requirements = {
            "min_length": Constants.MIN_PASSWORD_LENGTH,
            "requires_number": Constants.REQUIRES_NUMBER,
            "requires_symbol": Constants.REQUIRES_SYMBOL,
        }

    def send_verification_email(
        self,
        email: str,
        is_really_send_email: bool = True,
        store_user_data: bool = False,
        verification_code: str = None,
    ):
        """Sends a verification email to the user.

        This method checks if the user's email is already registered and verified.
        If the email is already registered, it returns an error message.
        If not, it generates a verification code (or uses the provided one),
        stores the user data (including the unverified status and verification code)
        in the database, and sends a verification email to the user (unless
        `is_really_send_email` is False).

        Args:
            email (str): The email address of the user.
            is_really_send_email (bool, optional): Whether to actually send the email.
                Defaults to True. If False, the email sending step is skipped.
            store_user_data (bool, optional): Whether to store the user data in the
                database. Defaults to False.
            verification_code (str, optional): The verification code to use. If None,
                a new code will be generated. Defaults to None.

        Returns:
            dict: A dictionary containing the result of the operation, including success
                status and a message.
        """
        # Check if user is already email verified, return error if so
        if self._is_user_verified(email):
            response = ApiResponse.error_response(
                data={}, message=ErrorMessages.EMAIL_REGISTERED
            )
            return response

        # Create verification code if not provided
        if verification_code is None:
            verification_code_str = create_verification_code()
        else:
            verification_code_str = verification_code

        # Store user data
        if store_user_data:
            user_data = {
                UserDataKeys.EMAIL: email,
                UserDataKeys.HASHED_PASSWORD: "",
                UserDataKeys.IS_VERIFIED: False,
                UserDataKeys.VERIFICATION_CODE: verification_code_str,
                UserDataKeys.PLAN: Plans.FREE,
                UserDataKeys.LOG_IN_STATUS: LoggedInStatus.NOT_LOGGED_IN,
            }

            self.db.update_data({email: user_data})
            self.db.dump_dirty_parts_to_pickles()

        # Send verification email
        if is_really_send_email:
            send_verification_email_with_code_only(
                env_path="./.env_email",
                receiver_email=email,
                verification_code=verification_code_str,
            )

            response = ApiResponse.success_response(
                data={}, message=SuccessMessages.VERIFICATION_EMAIL_SENT
            )

        return response

    def sign_up(self, email: str, password: str, verification_code: str) -> dict:
        """Registers a new user by verifying their email and storing their credentials.

        This function performs the following steps:
        1. Checks if the user's email is already registered (verified). If so, it returns an error.
        2. Verifies the provided verification code against the stored code for the given email.
           If the codes do not match, it returns an error.
        3. If the email is not registered and the verification code is correct, it proceeds to:
           - Hash the user's password for security.
           - Update the user's record in the database, marking the email as verified and storing
             the hashed password.
           - Clear the verification code from the user's record.

        Args:
            email (str): The email address of the user to register.
            password (str): The user's password.
            verification_code (str): The verification code received by the user via email.

        Returns:
            dict: A dictionary containing the result of the registration process, including a
                  success status and a message.
        """
        if self._is_user_verified(email):
            response = ApiResponse.error_response(
                data={}, message=ErrorMessages.EMAIL_REGISTERED
            )
            return response

        # Check if verification code is correct (Important step in this method)
        if not self.is_verification_code_match(
            email=email, verification_code=verification_code
        ):
            response = ApiResponse.error_response(
                data={}, message=ErrorMessages.VERIFICATION_CODE_INCORRECT
            )
            return response

        # Set IS_VERIFIED to True and remove the verification code
        # update the user data in the database
        hashed_password = hash_password(password)
        user_data = {
            UserDataKeys.EMAIL: email,
            UserDataKeys.HASHED_PASSWORD: hashed_password,
            UserDataKeys.IS_VERIFIED: True,
            UserDataKeys.VERIFICATION_CODE: "",
            UserDataKeys.PLAN: Plans.FREE,
            UserDataKeys.LOG_IN_STATUS: LoggedInStatus.NOT_LOGGED_IN,
        }
        self.db.update_data({email: user_data})
        self.db.dump_dirty_parts_to_pickles()

        response = ApiResponse.success_response(
            data={}, message=SuccessMessages.USER_REGISTERED
        )
        return response

    def sign_in(self, email, password):
        # Query the database for user data using the provided email. If no user data is found,
        # set the response to indicate that the user was not found.
        user_data = self.db.query_key(email, default=None)
        if user_data is None:
            response = ApiResponse.error_response(
                data={}, message=ErrorMessages.USER_NOT_FOUND
            )
            return response

        # Check if the provided password matches the stored hashed password.
        stored_hashed_pass = user_data[UserDataKeys.HASHED_PASSWORD].encode("utf-8")
        if not self.is_password_match(password, stored_hashed_pass):
            response = ApiResponse.error_response(
                data={}, message=ErrorMessages.PASSWORD_INCORRECT
            )
            return response

        # Generate JWT
        payload = {UserDataKeys.EMAIL: email}
        access_token = auth_utils.create_token(
            payload=payload,
            expires_delta=datetime.timedelta(minutes=60),
        )
        refresh_token = auth_utils.create_token(
            payload=payload,
            expires_delta=datetime.timedelta(minutes=43200),  # 60 x 24 x 30 = 43200
        )
        response = ApiResponse.success_response(
            data={
                ResponseKeys.ACCESS_TOKEN: access_token,
                ResponseKeys.REFRESH_TOKEN: refresh_token,
            },
            message=SuccessMessages.USER_LOGGED_IN,
        )
        return response

    def sign_in_with_access_token(self, access_token):
        # Validate the access token
        payload, error = auth_utils.validate_token(access_token)

        if error:
            response = ApiResponse.error_response(data={}, message=error)
            return response

        email = payload.get(UserDataKeys.EMAIL)

        # Query the database for user data using the email from the token.
        user_data = self.db.query_key(email, default=None)
        if user_data is None:
            response = ApiResponse.error_response(
                data={}, message=ErrorMessages.USER_NOT_FOUND
            )
            return response

        # Generate a new access token
        new_payload = {UserDataKeys.EMAIL: email}
        new_token = auth_utils.create_token(
            payload=new_payload,
        )

        response = ApiResponse.success_response(
            data={ResponseKeys.ACCESS_TOKEN: new_token},
            message=SuccessMessages.USER_LOGGED_IN,
        )

        return response

    def is_user_email_verified(self, email):
        user_data = self.db.query_key(email, default=None)
        if user_data is None:
            response = ApiResponse.error_response(
                data={}, message=ErrorMessages.USER_NOT_FOUND
            )
            return response
        is_verified = user_data.get(UserDataKeys.IS_VERIFIED, False)
        response = ApiResponse.success_response(
            data={ResponseKeys.IS_VERIFIED: is_verified},
            message=SuccessMessages.VERIFIED,
        )
        return response

    def is_password_match(self, password, stored_hashed_password):
        return verify_password(stored_hashed_password, password)

    def _is_user_verified(self, email: str) -> bool:
        """Checks if a user's email is verified.

        Args:
            email (str): The email address of the user.

        Returns:
            bool: True if the user is verified, False otherwise.

        Examples:
            ```python
            is_verified = user_db._is_user_verified(email="<EMAIL>")
            ```

            ```python
            is_verified = user_db._is_user_verified(email="<EMAIL>")
            ```
        """

        user_data = self.db.query_key(email)
        if not user_data:
            return False

        return user_data.get(UserDataKeys.IS_VERIFIED, False)

    def is_verification_code_match(self, email: str, verification_code: str) -> bool:
        """Checks if the provided verification code matches the stored verification code for a given email.

        Args:
            email (str): The email address of the user.
            verification_code (str): The verification code to check.

        Returns:
            bool: True if the verification codes match, False otherwise.

        Examples:
            ```python
            is_match = user_db.is_verification_code_match(email="<EMAIL>", verification_code="123456")
            ```

            ```python
            is_match = user_db.is_verification_code_match(email="<EMAIL>", verification_code="654321")
            ```
        """
        is_match = False

        assert self.db.is_key_in(email) == True

        user_data = self.db.query_key(email)
        stored_verification_code = user_data.get(UserDataKeys.VERIFICATION_CODE, None)
        is_match = stored_verification_code == verification_code

        return is_match

    def _get_user_verification_code(self, email):
        user_data = self.db.query_key(email)
        return user_data.get(UserDataKeys.VERIFICATION_CODE, None)

    def verify_user_email(self, email):
        """
        Verifies the user's email address.

        This method checks if the provided email exists in the database.
        If the email is found, it updates the user's verification status to
        True, indicating that the email has been verified. The updated user
        data is then saved back to the database, and any dirty parts are
        dumped to the pickle files for persistence.

        If the email is not found in the database, the response will indicate
        failure and provide an appropriate error message.
        """
        user_data = self.db.query_key(email, default=None)
        if user_data is None:
            response = ApiResponse.error_response(
                data={}, message=ErrorMessages.USER_NOT_FOUND
            )
            return response

        user_data[UserDataKeys.IS_VERIFIED] = True
        self.db.update_data({email: user_data})
        self.db.dump_dirty_parts_to_pickles()

        response = ApiResponse.success_response(
            data={}, message=SuccessMessages.EMAIL_VERIFIED
        )
        return response

    def request_password_reset(self, email):
        user_data = self.db.query_key(email, default=None)
        if user_data is None:
            response = ApiResponse.error_response(
                data={}, message=ErrorMessages.USER_NOT_FOUND
            )
            return response
        reset_token = self.generate_reset_token()
        user_data[UserDataKeys.RESET_TOKEN] = reset_token
        user_data[UserDataKeys.RESET_TOKEN_EXPIRES] = (
            datetime.datetime.now() + datetime.timedelta(hours=24)
        ).isoformat()
        self.db.update_data({email: user_data})
        self.db.dump_dirty_parts_to_pickles()
        response = ApiResponse.success_response(
            data={},
            message=SuccessMessages.PASSWORD_RESET_EMAIL_SENT,
        )
        return response

    def reset_password(self, token, new_password):
        for email in self.db.get_db_keys():
            user_data = self.db.query_key(email, default=None)
            if user_data and user_data.get(UserDataKeys.RESET_TOKEN) == token:
                if self.is_token_valid(user_data.get(UserDataKeys.RESET_TOKEN_EXPIRES)):
                    hashed_password = hash_password(new_password)
                    user_data[UserDataKeys.HASHED_PASSWORD] = hashed_password
                    del user_data[UserDataKeys.RESET_TOKEN]
                    del user_data[UserDataKeys.RESET_TOKEN_EXPIRES]
                    self.db.update_data({email: user_data})
                    self.db.dump_dirty_parts_to_pickles()
                    return ApiResponse.success_response(
                        data={}, message=SuccessMessages.PASSWORD_RESET
                    )
                else:
                    return ApiResponse.error_response(
                        data={}, message=ErrorMessages.TOKEN_EXPIRED
                    )
        return ApiResponse.error_response(
            data={},
            message=ErrorMessages.INVALID_TOKEN,
        )

    def update_user_profile(self, email, **kwargs):
        user_data = self.db.query_key(email, default=None)
        if user_data is None:
            return ApiResponse.error_response(
                data={},
                message=ErrorMessages.USER_NOT_FOUND,
            )

        for key, value in kwargs.items():
            if key in ["name", "address", "phone"]:
                user_data[key] = value

        self.db.update_data({email: user_data})
        self.db.dump_dirty_parts_to_pickles()

        return ApiResponse.success_response(
            data={},
            message=SuccessMessages.PROFILE_UPDATED,
        )

    def create_tokens_by_refresh_token(
        self, email: str, refresh_token: str
    ) -> Dict[str, Any]:
        """
        Validates a refresh token and creates new access and refresh tokens.

        Args:
        -----
        email: str
            The user's email address.
        refresh_token: str
            The refresh token to validate.

        Returns:
        --------
        Dict[str, Any]
            A dictionary containing the result of the operation, including new access and refresh tokens if successful.

        Note:
        -----
        If the refresh token is invalid, the function returns an error indicating that manual login is needed.

        Examples:
        ---------
        To create new tokens with a valid refresh token:
        ```python
        token_response = user_db.create_tokens_by_refresh_token(
            email="<EMAIL>", refresh_token=refresh_token
        )
        ```

        To handle an invalid refresh token:
        ```python
        token_response = user_db.create_tokens_by_refresh_token(
            email="<EMAIL>", refresh_token="invalid_refresh_token"
        )
        if not token_response[ResponseKeys.RESULT][ResponseKeys.SUCCESS]:
            print(token_response[ResponseKeys.RESULT][ResponseKeys.MESSAGE])
        ```
        """
        # Validate refresh token, if ok, create new access token and new refresh token
        # if not ok, return response of resfresh token expired, manual login needed
        payload, error = auth_utils.validate_token(refresh_token)

        if error:
            response = ApiResponse.error_response(data={}, message=error)
            return response

        if payload:
            access_token = auth_utils.create_access_token({"email": email})
            refresh_token = auth_utils.create_refresh_token({"email": email})
            response = ApiResponse.success_response(
                data={
                    ResponseKeys.ACCESS_TOKEN: access_token,
                    ResponseKeys.REFRESH_TOKEN: refresh_token,
                },
            )

        return response

    def delete_user_account(self, email):
        """Deletes a user account from the database.

        Args:
            email (str): The email address of the user account to delete.

        Returns:
            dict: A response dictionary containing the result of the operation.
        """
        if self.db.remove_key(email):
            response = ApiResponse.success_response(
                data={}, message=SuccessMessages.ACCOUNT_DELETED
            )
        else:
            response = ApiResponse.error_response(
                data={}, message=ErrorMessages.USER_NOT_FOUND
            )
        return response

    def get_all_users(self):
        """Gets all users from the database.

        Returns:
            dict: A response dictionary containing a list of all users with their email,
                  verification status and role.
        """
        all_users = []
        for email in self.db.get_db_keys():
            user_data = self.db.query_key(email, default=None)
            if user_data:
                all_users.append(
                    {
                        UserDataKeys.EMAIL: user_data.get(UserDataKeys.EMAIL),
                        UserDataKeys.IS_VERIFIED: user_data.get(
                            UserDataKeys.IS_VERIFIED
                        ),
                        UserDataKeys.ROLE: user_data.get(UserDataKeys.ROLE, Roles.USER),
                    }
                )

        response = ApiResponse.success_response(
            data={ResponseKeys.USERS: all_users},
            message=SuccessMessages.USERS_RETRIEVED,
        )
        return response

    def set_user_role(self, email: str, role: str) -> Dict[str, Any]:
        """Sets or updates a user's role in the database.

        Updates the role of a user identified by their email address. If the user is not found,
        returns an error response. If successful, updates the user's role and returns a success response.

        Args:
            email (str): The email address of the user whose role should be updated
            role (str): The new role to assign to the user

        Returns:
            Dict[str, Any]: A dictionary containing the result of the operation, including success status and message

        Examples:
            ```python
            # Set a user as an admin
            response = user_db.set_user_role(email="<EMAIL>", role=Roles.ADMIN)
            ```

            ```python
            # Set a user as a regular user
            response = user_db.set_user_role(email="<EMAIL>", role=Roles.USER)
            ```
        """
        response = {ResponseKeys.RESULT: {}}
        user_data = self.db.query_key(email, default=None)
        if user_data is None:
            response[ResponseKeys.RESULT][ResponseKeys.IS_SUCCESS] = False
            response[ResponseKeys.RESULT][
                ResponseKeys.MESSAGE
            ] = ErrorMessages.USER_NOT_FOUND
            return response
        user_data[UserDataKeys.ROLE] = role
        self.db.update_data({email: user_data})
        self.db.dump_dirty_parts_to_pickles()
        response[ResponseKeys.RESULT][ResponseKeys.IS_SUCCESS] = True
        response[ResponseKeys.RESULT][
            ResponseKeys.MESSAGE
        ] = SuccessMessages.ROLE_UPDATED
        return response

    def get_user_role(self, email):
        response = {ResponseKeys.RESULT: {}}
        user_data = self.db.query_key(email, default=None)
        if user_data is None:
            response[ResponseKeys.RESULT][ResponseKeys.ROLE] = None
            response[ResponseKeys.RESULT][
                ResponseKeys.MESSAGE
            ] = ErrorMessages.USER_NOT_FOUND
        else:
            response[ResponseKeys.RESULT][ResponseKeys.ROLE] = user_data.get(
                UserDataKeys.ROLE, Roles.USER
            )
            response[ResponseKeys.RESULT][
                ResponseKeys.MESSAGE
            ] = SuccessMessages.ROLE_RETRIEVED
        return response

    def is_password_strong(self, password):
        if len(password) < self.password_requirements["min_length"]:
            return False
        if self.password_requirements["requires_number"] and not any(
            char.isdigit() for char in password
        ):
            return False
        if self.password_requirements["requires_symbol"] and not any(
            char in "!@#$%^&*()-_+" for char in password
        ):
            return False
        return True

    def generate_reset_token(self):
        return hashlib.sha256(os.urandom(60)).hexdigest()

    def is_token_valid(self, expires):
        if expires:
            expires_datetime = datetime.datetime.fromisoformat(expires)
            return expires_datetime > datetime.datetime.now()
        return False

    def validate_access_token(
        self,
        token: str,
        required_keys: List[str] = None,
    ) -> Dict[str, Any]:
        """Validates an access token and checks for required keys in the payload.

        Validates the given access token by checking its signature, expiration, and required keys.
        Returns a response indicating success/failure and appropriate messages.

        Args:
            token (str): The access token to validate
            required_keys (List[str], optional): List of keys that must be present in token payload. Defaults to None.

        Returns:
            Dict[str, Any]: Response dictionary containing validation result and message

        Examples:
            ```python
            # Validate token with no required keys
            response = user_db.validate_access_token(token="eyJ0eXAiOiJKV1QiLCJhbGc...")
            ```

            ```python
            # Validate token requiring specific payload keys
            response = user_db.validate_access_token(
                token="eyJ0eXAiOiJKV1QiLCJhbGc...",
                required_keys=["user_id", "role"]
            )
            ```
        """
        response = {ResponseKeys.RESULT: {}}

        payload, error = auth_utils.validate_token(
            token,
            required_keys=required_keys,
        )

        if error:
            response[ResponseKeys.RESULT][ResponseKeys.IS_SUCCESS] = False

            if "Expired signature" in error:
                response[ResponseKeys.RESULT][
                    ResponseKeys.MESSAGE
                ] = ErrorMessages.TOKEN_EXPIRED
            elif "Invalid signature" in error:
                response[ResponseKeys.RESULT][
                    ResponseKeys.MESSAGE
                ] = ErrorMessages.TOKEN_INVALID_SIGNATURE
            elif "Invalid token" in error:
                response[ResponseKeys.RESULT][
                    ResponseKeys.MESSAGE
                ] = ErrorMessages.TOKEN_INVALID
            else:
                response[ResponseKeys.RESULT][ResponseKeys.MESSAGE] = error

            return response

        if payload:
            response[ResponseKeys.RESULT][ResponseKeys.IS_SUCCESS] = True
            response[ResponseKeys.RESULT][
                ResponseKeys.MESSAGE
            ] = SuccessMessages.ACCESS_TOKEN_VALIDATED

        return response

    def get_number_of_users(self):
        """
        Retrieves the total number of users in the database.

        Returns:
            int: The number of users in the database.
        """
        return self.db.get_db_num_keys()

    def get_num_free_users(self):
        """
        Retrieves the number of free users in the database.
        Returns:
            int: The number of free users in the database.
        """
        return self.db.get_num_data_by_key_value(key="plan", value=Plans.FREE)

    def upgrade_user_to_paid_plan(self, email):
        """
        Upgrades a user to the paid plan.
        Args:
            email (str): The email of the user to be upgraded.
        Returns:
            dict: A dictionary containing the result of the operation.
        """
        response = {ResponseKeys.RESULT: {}}
        user_data = self.db.query_key(email, default=None)

        if user_data is None:
            response[ResponseKeys.RESULT][ResponseKeys.IS_SUCCESS] = False
            response[ResponseKeys.RESULT][
                ResponseKeys.MESSAGE
            ] = ErrorMessages.USER_NOT_FOUND
            return response

        user_data[UserDataKeys.PLAN] = Plans.PAID
        self.db.update_data({email: user_data})
        self.db.dump_dirty_parts_to_pickles()

        response[ResponseKeys.RESULT][ResponseKeys.IS_SUCCESS] = True
        response[ResponseKeys.RESULT][
            ResponseKeys.MESSAGE
        ] = SuccessMessages.USER_UPGRADED
        return response

    def set_user_to_logged_in(self, email):
        """
        Sets a user to logged in.
        Args:
            email (str): The email of the user to be set as logged in.
        Returns:
            dict: A dictionary containing the result of the operation.
        """
        response = {ResponseKeys.RESULT: {}}
        user_data = self.db.query_key(email, default=None)

        if user_data is None:
            response[ResponseKeys.RESULT][ResponseKeys.IS_SUCCESS] = False
            response[ResponseKeys.RESULT][
                ResponseKeys.MESSAGE
            ] = ErrorMessages.USER_NOT_FOUND
            return response

        user_data[UserDataKeys.LOG_IN_STATUS] = LoggedInStatus.LOGGED_IN
        self.db.update_data({email: user_data})
        self.db.dump_dirty_parts_to_pickles()

        response[ResponseKeys.RESULT][ResponseKeys.IS_SUCCESS] = True
        response[ResponseKeys.RESULT][
            ResponseKeys.MESSAGE
        ] = SuccessMessages.USER_LOGGED_IN

        return response

    def get_num_paid_users(self):
        """
        Retrieves the number of paid users in the database.
        Returns:
            int: The number of paid users in the database.
        """
        return self.db.get_num_data_by_key_value(key="plan", value=Plans.PAID)

    def get_num_logged_in_users(self):
        """
        Retrieves the number of logged-in users in the database.
        Returns:
            int: The number of logged-in users in the database.
        """
        return self.db.get_num_data_by_key_value(
            key=UserDataKeys.LOG_IN_STATUS, value=LoggedInStatus.LOGGED_IN
        )

    def set_user_use_count(self, email: str, use_count: int) -> dict:
        """Sets the user use count.

        Args:
            email (str): The email of the user.
            use_count (int): The new use count to set for the user.

        Returns:
            dict: A dictionary containing the result of the operation.

        Examples:
            ```python
            response = user_db.set_user_use_count(email="<EMAIL>", use_count=10)
            ```

            ```python
            response = user_db.set_user_use_count(email="<EMAIL>", use_count=25)
            ```
        """
        response = {ResponseKeys.RESULT: {}}
        user_data = self.db.query_key(email, default=None)
        if user_data is None:
            response[ResponseKeys.RESULT][ResponseKeys.IS_SUCCESS] = False
            response[ResponseKeys.RESULT][
                ResponseKeys.MESSAGE
            ] = ErrorMessages.USER_NOT_FOUND
            return response
        user_data[UserDataKeys.USE_COUNT] = use_count
        self.db.update_data({email: user_data})
        self.db.dump_dirty_parts_to_pickles()
        response[ResponseKeys.RESULT][ResponseKeys.IS_SUCCESS] = True
        response[ResponseKeys.RESULT][
            ResponseKeys.MESSAGE
        ] = SuccessMessages.USE_COUNT_UPDATED
        return response

    def get_user_use_count(self, email: str) -> dict:
        """Gets the user use count.

        Args:
            email (str): The email of the user.

        Returns:
            dict: A dictionary containing the result of the operation.

        Examples:
            ```python
            response = user_db.get_user_use_count(email="<EMAIL>")
            ```

            ```python
            response = user_db.get_user_use_count(email="<EMAIL>")
            ```
        """
        response = {ResponseKeys.RESULT: {}}
        user_data = self.db.query_key(email, default=None)
        if user_data is None:
            response[ResponseKeys.RESULT][ResponseKeys.ROLE] = None
            response[ResponseKeys.RESULT][
                ResponseKeys.MESSAGE
            ] = ErrorMessages.USER_NOT_FOUND
        else:
            response[ResponseKeys.RESULT][ResponseKeys.USE_COUNT] = user_data.get(
                UserDataKeys.USE_COUNT, Roles.USER
            )
            response[ResponseKeys.RESULT][
                ResponseKeys.MESSAGE
            ] = SuccessMessages.USE_COUNT_RETRIEVED
        return response

    def _add_or_set_user_to_db(
        self,
        email: str,
        password: str = "",
        role: str = Roles.USER,
        use_count: int = 0,
        log_in_status: str = LoggedInStatus.NOT_LOGGED_IN,
        is_verified: bool = False,
        verification_code: str = None,
    ) -> None:
        """Adds a new user or updates an existing user in the database.

        Args:
            email (str): The email address of the user.
            password (str): The password for the user.
            role (str, optional): The role of the user. Defaults to Roles.USER.
            use_count (int, optional): The use count of the user. Defaults to 0.
            log_in_status (str, optional): The log-in status of the user. Defaults to LoggedInStatus.NOT_LOGGED_IN.
            is_verified (bool, optional): Whether the user is verified. Defaults to False.
            verification_code (str, optional): The verification code of the user. Defaults to None, if not given, create it.

        Examples:
            ```python
            user_db._add_or_set_user_to_db(
                email="<EMAIL>",
                password="Password123!",
                is_verified=True
            )
            ```

            ```python
            user_db._add_or_set_user_to_db(
                email="<EMAIL>",
                password="AnotherPassword",
                role=Roles.ADMIN,
                use_count=10
            )
            ```
        """

        if verification_code is None:
            verification_code = generate_random_integer_string(
                Constants.VERIFICATION_CODE_LENGTH
            )

        if password == "":
            hashed_password = ""
        else:
            hashed_password = hash_password(password)

        user_data = {
            UserDataKeys.EMAIL: email,
            UserDataKeys.HASHED_PASSWORD: hashed_password,
            UserDataKeys.PLAN: role,
            UserDataKeys.IS_VERIFIED: is_verified,
            UserDataKeys.LOG_IN_STATUS: log_in_status,
            UserDataKeys.USE_COUNT: use_count,
            UserDataKeys.ROLE: role,
            UserDataKeys.VERIFICATION_CODE: verification_code,
        }
        self.db.update_data({email: user_data})
        self.db.dump_dirty_parts_to_pickles()

    def obtain_verification_code(
        self, email: str, store_user_data: bool = True
    ) -> Dict[str, Any]:
        """Retrieves or generates a verification code for a user.

        If the user exists and is not verified, a new verification code is generated.
        If the user does not exist, a new user entry is created with the provided
        email and a new verification code. If the user exists and is already verified,
        an error message is returned.

        Args:
            email (str): The email address of the user.
            store_user_data (bool, optional): Whether to store the user data if the user does not exist. Defaults to True.

        Returns:
            Dict[str, Any]: A dictionary containing the result of the operation,
                           including a success status, a message, and the verification code
                           if successful.

        Examples:
            ```python
            response = user_db.obtain_verification_code(email="<EMAIL>")
            ```

            ```python
            response = user_db.obtain_verification_code(email="<EMAIL>")
            ```
        """
        response = {ResponseKeys.RESULT: {}}

        user_data = self.db.query_key(email, default=None)

        # If there is such user
        if user_data:
            # If the user is not verified,
            if not user_data[UserDataKeys.IS_VERIFIED]:
                # generate a new verification code
                new_verification_code = generate_random_integer_string(
                    Constants.VERIFICATION_CODE_LENGTH
                )
                user_data[UserDataKeys.VERIFICATION_CODE] = new_verification_code
                self.db.update_data({email: user_data})
                self.db.dump_dirty_parts_to_pickles()

                # response a new verification code
                response[ResponseKeys.RESULT][ResponseKeys.IS_SUCCESS] = True
                response[ResponseKeys.RESULT][
                    ResponseKeys.VERIFICATION_CODE
                ] = new_verification_code
                response[ResponseKeys.RESULT][
                    ResponseKeys.MESSAGE
                ] = SuccessMessages.GENERATED_VERIFICATION_CODE

            # If the user is verified,
            else:
                # response the error message (User is already registered)
                response[ResponseKeys.RESULT][ResponseKeys.IS_SUCCESS] = False
                response[ResponseKeys.RESULT][
                    ResponseKeys.MESSAGE
                ] = ErrorMessages.USER_REGISTERED

        # If there is no such user,
        if user_data is None:
            # store the user with verification code and IS_VERIFIED as False
            verification_code = generate_random_integer_string(
                Constants.VERIFICATION_CODE_LENGTH
            )
            user_data = {
                UserDataKeys.EMAIL: email,
                UserDataKeys.HASHED_PASSWORD: "",  # no need to hash the password here
                UserDataKeys.IS_VERIFIED: False,  # here
                UserDataKeys.VERIFICATION_CODE: verification_code,  # here
                UserDataKeys.PLAN: Plans.FREE,
                UserDataKeys.LOG_IN_STATUS: LoggedInStatus.NOT_LOGGED_IN,
            }

            # update the database
            if store_user_data:
                self.db.update_data({email: user_data})
                self.db.dump_dirty_parts_to_pickles()

            #   response the verification code
            response[ResponseKeys.RESULT][ResponseKeys.IS_SUCCESS] = True
            response[ResponseKeys.RESULT][
                ResponseKeys.VERIFICATION_CODE
            ] = verification_code
            response[ResponseKeys.RESULT][
                ResponseKeys.MESSAGE
            ] = SuccessMessages.GENERATED_VERIFICATION_CODE

        return response

    def merge_responses(self, response_list: List[Dict]) -> Dict:
        """Merges a list of response dictionaries into a single response.

        The function iterates through the list of responses, merging the
        results into a single dictionary.  If any response indicates failure,
        the merged response will also indicate failure. Messages from all
        responses are concatenated into a single message. Other keys in the
        result dictionaries are added to the merged response.

        Args:
            response_list (List[Dict]): A list of response dictionaries to merge.

        Returns:
            Dict: A merged response dictionary.

        Examples:
            ```python
            response1 = {ResponseKeys.RESULT: {ResponseKeys.SUCCESS: True, ResponseKeys.MESSAGE: "Operation 1 successful"}}
            response2 = {ResponseKeys.RESULT: {ResponseKeys.SUCCESS: True, ResponseKeys.MESSAGE: "Operation 2 successful"}}
            merged_response = self.merge_responses(response_list=[response1, response2])
            # merged_response will be:
            # {ResponseKeys.RESULT: {ResponseKeys.SUCCESS: True, ResponseKeys.MESSAGE: "Operation 1 successful Operation 2 successful "}}
            ```

            ```python
            response1 = {ResponseKeys.RESULT: {ResponseKeys.SUCCESS: True, ResponseKeys.MESSAGE: "Operation 1 successful"}}
            response2 = {ResponseKeys.RESULT: {ResponseKeys.SUCCESS: False, ResponseKeys.MESSAGE: "Operation 2 failed"}}
            merged_response = self.merge_responses(response_list=[response1, response2])
            # merged_response will be:
            # {ResponseKeys.RESULT: {ResponseKeys.SUCCESS: False, ResponseKeys.MESSAGE: "Operation 1 successful Operation 2 failed "}}
            ```
        """
        merged_response = {
            ResponseKeys.RESULT: {
                ResponseKeys.IS_SUCCESS: True,
                ResponseKeys.MESSAGE: "",
            }
        }

        for response in response_list:
            for key in response[ResponseKeys.RESULT].keys():
                if key == ResponseKeys.IS_SUCCESS:
                    if not response[ResponseKeys.RESULT][ResponseKeys.IS_SUCCESS]:
                        merged_response[ResponseKeys.RESULT][
                            ResponseKeys.IS_SUCCESS
                        ] = False
                elif key == ResponseKeys.MESSAGE:
                    merged_response[ResponseKeys.RESULT][ResponseKeys.MESSAGE] += (
                        response[ResponseKeys.RESULT][ResponseKeys.MESSAGE] + " "
                    )
                else:
                    merged_response[ResponseKeys.RESULT][key] = response[
                        ResponseKeys.RESULT
                    ][key]

        return merged_response

    def obtain_verification_code_and_send_email(self, email: str) -> Dict:
        response = {ResponseKeys.RESULT: {}}

        # Obtain the verification code
        response = self.obtain_verification_code(
            email=email,
            store_user_data=False,
        )

        verification_code = response[ResponseKeys.RESULT][
            ResponseKeys.VERIFICATION_CODE
        ]

        # Send the email
        if response[ResponseKeys.RESULT][ResponseKeys.IS_SUCCESS]:
            _response = self.send_verification_email(
                email=email,
                is_really_send_email=True,
                store_user_data=False,
                verification_code=verification_code,
            )

            response = self.merge_responses([response, _response])

        # Add or set the user to the database
        self._add_or_set_user_to_db(email, verification_code=verification_code)

        return response
